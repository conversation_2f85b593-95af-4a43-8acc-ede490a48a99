import Handsontable from 'handsontable'
import { observer } from 'mobx-react'
import { useEffect } from 'react'
import { useStore } from 'stores/useStore'
import { SpreadsheetReact } from 'widgets/SpreadsheetReact'

import cls from './DisplaySettings.module.scss'

type GridSettings = Handsontable.GridSettings

export const DisplaySettings = observer(() => {
  const { calcModelStore } = useStore()
  const {
    listOfStationsStore: { selectedPlant, displaySettingsStore },
  } = calcModelStore
  const { formattedDate } = calcModelStore
  const { getDisplaySettings, displaySettingsSpreadsheetData, updateDisplaySettingsData } = displaySettingsStore
  const { columns, data, nestedHeaders, cell, rowHeaders } = displaySettingsSpreadsheetData

  /**
   * Хук afterGetColHeader
   *
   * Функция-обработчик, стилизующая заголовки столбцов.
   * Если индекс столбца равен -1, используется для отображения наименования формы.
   *
   * @param col - индекс столбца (если равен -1, это наименование формы).
   * @param TH - HTML-элемент, в который выводится заголовок столбца.
   */
  const afterGetColHeader: GridSettings['afterGetColHeader'] = (col, TH, level) => {
    const isFormNameColumn = col === -1 && level === 0
    if (isFormNameColumn) {
      TH.textContent = 'Наименование формы'
    }

    TH.style.fontWeight = 'bold'
  }

  useEffect(() => {
    if (selectedPlant?.plantId) {
      getDisplaySettings(selectedPlant.plantId, formattedDate)
    }
  }, [selectedPlant])

  return (
    <div className={cls.container}>
      <h3 className={cls.title}>Расчетная таблица</h3>
      <SpreadsheetReact
        data={data}
        columns={columns}
        nestedHeaders={nestedHeaders}
        cell={cell}
        rowHeaders={rowHeaders}
        rowHeaderWidth={150}
        afterGetColHeader={afterGetColHeader}
        afterChange={updateDisplaySettingsData}
      />
    </div>
  )
})
