import { CalculationColumn } from 'entities/shared/common.entities'
import { IGetSpreadsheetData } from 'entities/shared/spreadsheetDataResponse'
import { axiosInstance as api } from 'shared/lib/axios'

// Станция или Свод
export type Section = 'PLANT' | 'SUMMARY'

// Ячейка с флагом видимости
export interface DisplaySettingsCell {
  value: boolean
  column: keyof typeof CalculationColumn
  section: Section
}

// Тело запроса на сохранение
export interface HiddenColumnsRequest {
  /** идентификатор станции */
  plantId: number
  /** дата начала действия в формате 'YYYY-MM-DD' */
  date: string
  /** список колонок с флагами видимости */
  columns: DisplaySettingsCell[]
}

// Получение таблицы настройки отображения колонок на определённую дату
export const getDisplaySettings = (
  plantId: number,
  date: string,
): Promise<IGetSpreadsheetData<DisplaySettingsCell>> => {
  return api.get(`/api/v1/plant/${plantId}/hidden-columns`, { params: { date } })
}

// Сохранение настроек отображения колонок на определённую дату
export const saveDisplaySettings = (
  settings: HiddenColumnsRequest,
): Promise<IGetSpreadsheetData<DisplaySettingsCell>> => {
  return api.put(`/api/v1/plant/hidden-columns`, settings)
}
