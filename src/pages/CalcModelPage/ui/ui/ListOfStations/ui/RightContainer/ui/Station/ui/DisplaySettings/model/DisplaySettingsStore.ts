import { makeAutoObservable, runInAction } from 'mobx'
import { klona } from 'shared/lib/klona'
import { RootStore } from 'stores/RootStore'
import { convertSpreadsheetResponseToComponentProps } from 'widgets/Spreadsheet/ui/lib'

import { DisplaySettingsCell, getDisplaySettings, HiddenColumnsRequest, saveDisplaySettings } from '../api'
import { DisplaySettingsSpreadsheetBeforeConvertProps, IDisplaySettingsStore } from './DisplaySettingsStore.types'

const emptySpreadsheetStructure: IDisplaySettingsStore['displaySettingsSpreadsheetData'] = {
  columns: [],
  data: [],
  nestedHeaders: [],
  cell: [],
  rowHeaders: [],
}

export class DisplaySettingsStore implements IDisplaySettingsStore {
  rootStore: IDisplaySettingsStore['rootStore']
  displaySettingsSpreadsheetData: IDisplaySettingsStore['displaySettingsSpreadsheetData'] = {
    ...emptySpreadsheetStructure,
  }
  originalDisplaySettingsData: IDisplaySettingsStore['originalDisplaySettingsData'] = {
    ...emptySpreadsheetStructure,
  }
  isDisplaySettingsLoading: IDisplaySettingsStore['isDisplaySettingsLoading'] = false
  currentPlantId: IDisplaySettingsStore['currentPlantId'] = null
  currentDate: IDisplaySettingsStore['currentDate'] = null

  constructor(rootStore: RootStore) {
    this.rootStore = rootStore
    makeAutoObservable(this)
  }

  get isReadOnly(): IDisplaySettingsStore['isReadOnly'] {
    const hasNotRights = !this.rootStore.calcModelStore.editMode
    const isViewOnly = this.rootStore.calcModelStore.listOfStationsStore.selectedPlant?.viewOnly
    const isPastDate = this.rootStore.calcModelStore.isPastDate
    const isGAES = this.rootStore.calcModelStore.listOfStationsStore.selectedPlant?.type === 'GAES'

    return hasNotRights || isViewOnly || isPastDate || isGAES
  }

  private _applyConvertedData(converted: DisplaySettingsSpreadsheetBeforeConvertProps) {
    const columns = converted.columns.map(() => ({
      type: 'checkbox',
      className: 'htCenter' + (this.isReadOnly ? ' disabledCell' : ''),
      readOnly: this.isReadOnly,
    }))

    runInAction(() => {
      this.originalDisplaySettingsData = { ...converted, columns }
      this.displaySettingsSpreadsheetData = { ...converted, columns }
    })
  }

  getDisplaySettings: IDisplaySettingsStore['getDisplaySettings'] = async (plantId, date) => {
    try {
      this.isDisplaySettingsLoading = true
      this.currentPlantId = plantId
      this.currentDate = date

      const res = await getDisplaySettings(plantId, date)
      const converted = convertSpreadsheetResponseToComponentProps(res)

      this._applyConvertedData(converted)
    } catch (e) {
      console.error(e)
    } finally {
      runInAction(() => {
        this.isDisplaySettingsLoading = false
      })
    }
  }

  updateDisplaySettingsData: IDisplaySettingsStore['updateDisplaySettingsData'] = (changes) => {
    if (!changes) return

    changes.forEach(([row, col, _, newValue]) => {
      if (typeof col === 'number') {
        this.displaySettingsSpreadsheetData.data[row][col] = newValue
      }
    })
  }

  get isDisplaySettingsChanged(): IDisplaySettingsStore['isDisplaySettingsChanged'] {
    return JSON.stringify(this.displaySettingsSpreadsheetData) !== JSON.stringify(this.originalDisplaySettingsData)
  }

  saveDisplaySettings: IDisplaySettingsStore['saveDisplaySettings'] = async () => {
    if (!this.currentPlantId || !this.currentDate || !this.originalDisplaySettingsData) {
      return
    }

    try {
      // Подготавливаем данные для отправки
      const columnsForRequest: DisplaySettingsCell[] = []

      this.displaySettingsSpreadsheetData.cell.forEach((cell) => {
        columnsForRequest.push({
          value: this.displaySettingsSpreadsheetData.data[cell.row][cell.col],
          column: cell.column,
          section: cell.section,
        })
      })

      const requestData: HiddenColumnsRequest = {
        plantId: this.currentPlantId,
        date: this.currentDate,
        columns: columnsForRequest,
      }

      const res = await saveDisplaySettings(requestData)
      const converted = convertSpreadsheetResponseToComponentProps(res)

      this._applyConvertedData(converted)

      this.rootStore.notificationStore.addNotification({
        title: 'Сохранение РМ',
        description: 'Изменения сохранены',
        type: 'success',
      })
    } catch (e) {
      console.error('Возникла ошибка при сохранении:', e)
    }
  }

  resetDisplaySettings: IDisplaySettingsStore['resetDisplaySettings'] = () => {
    this.displaySettingsSpreadsheetData = klona(this.originalDisplaySettingsData)
  }
}
