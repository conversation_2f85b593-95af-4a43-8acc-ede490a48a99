import Handsontable from 'handsontable'
import { RootStore } from 'stores/RootStore'
import { SpreadsheetBaseProps } from 'widgets/Spreadsheet/ui/lib'

import { DisplaySettingsCell } from '../api'

type GridSettings = Handsontable.GridSettings

interface DisplaySettingsSpreadsheetColumnProps {
  readOnly: boolean
  type: string
  className: string | string[]
}
export type DisplaySettingsSpreadsheetBeforeConvertProps = SpreadsheetBaseProps<
  Handsontable.ColumnSettings,
  DisplaySettingsCell
>

type DisplaySettingsSpreadsheetProps = SpreadsheetBaseProps<DisplaySettingsSpreadsheetColumnProps, DisplaySettingsCell>

export interface IDisplaySettingsStore {
  rootStore: RootStore

  displaySettingsSpreadsheetData: DisplaySettingsSpreadsheetProps
  originalDisplaySettingsData: DisplaySettingsSpreadsheetProps

  isDisplaySettingsLoading: boolean
  isDisplaySettingsChanged: boolean
  isReadOnly: boolean

  currentPlantId: number | null
  currentDate: string | null

  getDisplaySettings: (plantId: number, date: string) => Promise<void>
  updateDisplaySettingsData: GridSettings['afterChange']
  saveDisplaySettings: () => Promise<void>
  resetDisplaySettings: () => void
}
