import { describe, expect, it } from 'vitest'
import { CalculationColumn } from 'entities/shared/common.entities'

import {
  COLUMN_BLOCK_SIZES,
  COLUMN_POSITIONS,
  getGesCellPropByTableCoords,
  getRguColumnIndex,
  getStationColumnIndex,
  isColumnHidden,
  getDynamicColumnBlockSizes,
  getDynamicColumnPositions,
} from './helpers'

describe('Column Index Calculations', () => {
  // Используем 2 РГЕ как базовый случай для всех тестов
  const rguCount = 2

  describe('getStationColumnIndex', () => {
    it('should calculate correct indices for RESULTS block', () => {
      expect(getStationColumnIndex('RESULTS', COLUMN_POSITIONS.P_MIN_RESULT, rguCount)).toBe(0)
      expect(getStationColumnIndex('RESULTS', COLUMN_POSITIONS.P_GEN, rguCount)).toBe(1)
      expect(getStationColumnIndex('RESULTS', COLUMN_POSITIONS.P_MAX_RESULT, rguCount)).toBe(2)
    })

    it('should calculate correct indices for RESERVES block', () => {
      // После RESULTS: 3 * (2 РГЕ + 1 станция) = 9
      const baseIndex = COLUMN_BLOCK_SIZES.RESULTS * (rguCount + 1)
      expect(baseIndex).toBe(9)
      expect(getStationColumnIndex('RESERVES', COLUMN_POSITIONS.RESERVES_MAX, rguCount)).toBe(baseIndex + 0)
      expect(getStationColumnIndex('RESERVES', COLUMN_POSITIONS.AVRCHM_LOAD, rguCount)).toBe(baseIndex + 1)
      expect(getStationColumnIndex('RESERVES', COLUMN_POSITIONS.NPRCH, rguCount)).toBe(baseIndex + 2)
    })

    it('should calculate correct indices for LIMITS block', () => {
      // После RESULTS (9) + RESERVES (9) = 18
      const baseIndex = COLUMN_BLOCK_SIZES.RESULTS * (rguCount + 1) + COLUMN_BLOCK_SIZES.RESERVES * (rguCount + 1)
      expect(baseIndex).toBe(18)
      expect(getStationColumnIndex('LIMITS', COLUMN_POSITIONS.P_MIN, rguCount)).toBe(baseIndex + 0)
      expect(getStationColumnIndex('LIMITS', COLUMN_POSITIONS.P_MAX, rguCount)).toBe(baseIndex + 1)
    })

    it('should calculate correct indices for CM block', () => {
      // После RESULTS (9) + RESERVES (9) + LIMITS (6) = 24
      const baseIndex =
        COLUMN_BLOCK_SIZES.RESULTS * (rguCount + 1) +
        COLUMN_BLOCK_SIZES.RESERVES * (rguCount + 1) +
        COLUMN_BLOCK_SIZES.LIMITS * (rguCount + 1)
      expect(baseIndex).toBe(24)
      expect(getStationColumnIndex('CM', COLUMN_POSITIONS.CM_P_MIN, rguCount)).toBe(baseIndex + 0)
      expect(getStationColumnIndex('CM', COLUMN_POSITIONS.CM_P_MAX, rguCount)).toBe(baseIndex + 1)
    })

    it('should calculate correct indices for MODES block', () => {
      // После ... + CM (6) = 30
      const baseIndex =
        COLUMN_BLOCK_SIZES.RESULTS * (rguCount + 1) +
        COLUMN_BLOCK_SIZES.RESERVES * (rguCount + 1) +
        COLUMN_BLOCK_SIZES.LIMITS * (rguCount + 1) +
        COLUMN_BLOCK_SIZES.CM * (rguCount + 1)
      expect(baseIndex).toBe(30)
      expect(getStationColumnIndex('MODES', COLUMN_POSITIONS.MODES_P_MIN, rguCount)).toBe(baseIndex + 0)
      expect(getStationColumnIndex('MODES', COLUMN_POSITIONS.MODES_P_MAX, rguCount)).toBe(baseIndex + 1)
      expect(getStationColumnIndex('MODES', COLUMN_POSITIONS.MODES_DECLARED, rguCount)).toBe(baseIndex + 2)
    })

    it('should calculate correct index for CONSUMPT column', () => {
      // После ... + MODES (9) = 39
      const baseIndex =
        COLUMN_BLOCK_SIZES.RESULTS * (rguCount + 1) +
        COLUMN_BLOCK_SIZES.RESERVES * (rguCount + 1) +
        COLUMN_BLOCK_SIZES.LIMITS * (rguCount + 1) +
        COLUMN_BLOCK_SIZES.CM * (rguCount + 1) +
        COLUMN_BLOCK_SIZES.MODES * (rguCount + 1)
      expect(baseIndex).toBe(39)
      expect(getStationColumnIndex('CONSUMPT', COLUMN_POSITIONS.CONSUMPT, rguCount)).toBe(baseIndex + 0)
    })
  })

  describe('getRguColumnIndex', () => {
    const rguCount = 2 // Тестируем с 2 РГЕ
    const rguIndex = 0 // Индекс первого РГЕ
    const lastRguIndex = rguCount - 1 // Индекс последнего РГЕ (в данном случае, второго)

    it('should calculate correct indices for the first RGU', () => {
      // RESULTS: Станция (3) -> РГЕ0
      expect(getRguColumnIndex('RESULTS', COLUMN_POSITIONS.P_GEN, rguIndex, rguCount)).toBe(4)
      // RESERVES: Начало блока (9) + Станция (3) -> РГЕ0
      expect(getRguColumnIndex('RESERVES', COLUMN_POSITIONS.AVRCHM_LOAD, rguIndex, rguCount)).toBe(13)
      // LIMITS: Начало блока (18) + Станция (2) -> РГЕ0
      expect(getRguColumnIndex('LIMITS', COLUMN_POSITIONS.P_MIN, rguIndex, rguCount)).toBe(20)
      // CM: Начало блока (24) + Станция (2) -> РГЕ0
      expect(getRguColumnIndex('CM', COLUMN_POSITIONS.CM_P_MIN, rguIndex, rguCount)).toBe(26)
      // MODES: Начало блока (30) + Станция (3) -> РГЕ0
      expect(getRguColumnIndex('MODES', COLUMN_POSITIONS.MODES_P_MIN, rguIndex, rguCount)).toBe(33)
    })

    it('should calculate correct indices for the last RGU', () => {
      // RESULTS: Станция (3) + РГЕ0 (3) -> РГЕ1
      expect(getRguColumnIndex('RESULTS', COLUMN_POSITIONS.P_GEN, lastRguIndex, rguCount)).toBe(7)
      // RESERVES: Начало (9) + Станция (3) + РГЕ0 (3) -> РГЕ1
      expect(getRguColumnIndex('RESERVES', COLUMN_POSITIONS.AVRCHM_LOAD, lastRguIndex, rguCount)).toBe(16)
      // LIMITS: Начало (18) + Станция (2) + РГЕ0 (2) -> РГЕ1
      expect(getRguColumnIndex('LIMITS', COLUMN_POSITIONS.P_MIN, lastRguIndex, rguCount)).toBe(22)
      // CM: Начало (24) + Станция (2) + РГЕ0 (2) -> РГЕ1
      expect(getRguColumnIndex('CM', COLUMN_POSITIONS.CM_P_MIN, lastRguIndex, rguCount)).toBe(28)
      // MODES: Начало (30) + Станция (3) + РГЕ0 (3) -> РГЕ1
      expect(getRguColumnIndex('MODES', COLUMN_POSITIONS.MODES_P_MIN, lastRguIndex, rguCount)).toBe(36)
    })
  })

  describe('Constants validation', () => {
    it('should have correct block sizes', () => {
      expect(COLUMN_BLOCK_SIZES.RESULTS).toBe(3)
      expect(COLUMN_BLOCK_SIZES.RESERVES).toBe(3)
      expect(COLUMN_BLOCK_SIZES.LIMITS).toBe(2)
      expect(COLUMN_BLOCK_SIZES.CM).toBe(2)
      expect(COLUMN_BLOCK_SIZES.MODES).toBe(3)
      expect(COLUMN_BLOCK_SIZES.CONSUMPT).toBe(1)
    })

    it('should have correct column positions', () => {
      // Блок "Итог"
      expect(COLUMN_POSITIONS.P_MIN_RESULT).toBe(0)
      expect(COLUMN_POSITIONS.P_GEN).toBe(1)
      expect(COLUMN_POSITIONS.P_MAX_RESULT).toBe(2)

      // Блок "Резервы"
      expect(COLUMN_POSITIONS.RESERVES_MAX).toBe(0)
      expect(COLUMN_POSITIONS.AVRCHM_LOAD).toBe(1)
      expect(COLUMN_POSITIONS.NPRCH).toBe(2)

      // Блок "Итог.ОГР"
      expect(COLUMN_POSITIONS.P_MIN).toBe(0)
      expect(COLUMN_POSITIONS.P_MAX).toBe(1)

      // Блок "РМ"
      expect(COLUMN_POSITIONS.CM_P_MIN).toBe(0)
      expect(COLUMN_POSITIONS.CM_P_MAX).toBe(1)

      // Блок "Модес"
      expect(COLUMN_POSITIONS.MODES_P_MIN).toBe(0)
      expect(COLUMN_POSITIONS.MODES_P_MAX).toBe(1)
      expect(COLUMN_POSITIONS.MODES_DECLARED).toBe(2)

      // Блок "ИСП"
      expect(COLUMN_POSITIONS.CONSUMPT).toBe(0)
    })
  })

  describe('getGesCellPropByTableCoords', () => {
    const mockRows = [
      {
        cells: [
          { column: 'P_MIN_RESULT', value: 100, manual: true },
          { column: 'P_GEN', value: 200, manual: false },
          { column: 'P_MAX_RESULT', value: 300, manual: true },
          { column: 'RESERVES_MAX', value: 50, manual: false },
          { column: 'AVRCHM_LOAD', value: 25, manual: true },
          { column: 'NPRCH', value: 10, manual: false },
          { column: 'LIMIT_MIN', value: 80, manual: true },
          { column: 'LIMIT_MAX', value: 320, manual: false },
          { column: 'CM_P_MIN', value: 90, manual: true },
          { column: 'CM_P_MAX', value: 310, manual: false },
        ],
      },
    ] as any

    const mockRgus = [{ rows: [{ cells: [] }] }, { rows: [{ cells: [] }] }] as any

    it('should return correct cell for RESERVES_MAX column', () => {
      // RESERVES_MAX находится в позиции 9 (начало блока RESERVES для станции)
      const reservesMaxIndex = getStationColumnIndex('RESERVES', COLUMN_POSITIONS.RESERVES_MAX, 2)
      expect(reservesMaxIndex).toBe(9)

      const cell = getGesCellPropByTableCoords(mockRows, mockRgus, 0, reservesMaxIndex)
      expect(cell).toBeDefined()
      expect(cell?.column).toBe('RESERVES_MAX')
      expect(cell?.value).toBe(50)
      expect(cell?.manual).toBe(false)
    })

    it('should return undefined for non-existent column', () => {
      // Тест для колонки, которой нет в данных
      const nonExistentIndex = 999
      const cell = getGesCellPropByTableCoords(mockRows, mockRgus, 0, nonExistentIndex)
      expect(cell).toBeUndefined()
    })

    it('should return correct cell for NPRCH column', () => {
      // NPRCH находится в позиции 11 (RESERVES блок, позиция 2 для станции)
      const nprchIndex = getStationColumnIndex('RESERVES', COLUMN_POSITIONS.NPRCH, 2)
      expect(nprchIndex).toBe(11)

      const cell = getGesCellPropByTableCoords(mockRows, mockRgus, 0, nprchIndex)
      expect(cell).toBeDefined()
      expect(cell?.column).toBe('NPRCH')
      expect(cell?.value).toBe(10)
      expect(cell?.manual).toBe(false)
    })

    it('should return correct cell for LIMIT_MIN column (LIMITS block)', () => {
      // LIMIT_MIN находится в позиции 18 (LIMITS блок, позиция 0 для станции)
      const pMinIndex = getStationColumnIndex('LIMITS', COLUMN_POSITIONS.P_MIN, 2)
      expect(pMinIndex).toBe(18)

      const cell = getGesCellPropByTableCoords(mockRows, mockRgus, 0, pMinIndex)
      expect(cell).toBeDefined()
      expect(cell?.column).toBe('LIMIT_MIN') // В gesStationBlockByLevels используется P_MIN
      expect(cell?.value).toBe(80)
      expect(cell?.manual).toBe(true)
    })

    it('should return correct cell for CM_P_MAX column', () => {
      // CM_P_MAX находится в позиции 25 (CM блок, позиция 1 для станции)
      const cmPMaxIndex = getStationColumnIndex('CM', COLUMN_POSITIONS.CM_P_MAX, 2)
      expect(cmPMaxIndex).toBe(25)

      const cell = getGesCellPropByTableCoords(mockRows, mockRgus, 0, cmPMaxIndex)
      expect(cell).toBeDefined()
      expect(cell?.column).toBe('CM_P_MAX')
      expect(cell?.value).toBe(310)
      expect(cell?.manual).toBe(false)
    })

    it('should handle edge case when RESERVES_MAX is undefined in data', () => {
      // Создаем данные без RESERVES_MAX
      const mockRowsWithoutReservesMax = [
        {
          cells: [
            { column: 'P_MIN_RESULT', value: 100, manual: true },
            { column: 'P_GEN', value: 200, manual: false },
            { column: 'P_MAX_RESULT', value: 300, manual: true },
            // RESERVES_MAX отсутствует
            { column: 'AVRCHM_LOAD', value: 25, manual: true },
            { column: 'NPRCH', value: 10, manual: false },
            { column: 'LIMIT_MIN', value: 80, manual: true },
            { column: 'LIMIT_MAX', value: 320, manual: false },
            { column: 'CM_P_MIN', value: 90, manual: true },
            { column: 'CM_P_MAX', value: 310, manual: false },
          ],
        },
      ] as any

      const reservesMaxIndex = getStationColumnIndex('RESERVES', COLUMN_POSITIONS.RESERVES_MAX, 2)
      const cell = getGesCellPropByTableCoords(mockRowsWithoutReservesMax, mockRgus, 0, reservesMaxIndex)
      expect(cell).toBeUndefined()
    })
  })
})

describe('Hidden Columns Functionality', () => {
  describe('isColumnHidden', () => {
    it('should return true if column is in hidden list', () => {
      const hiddenColumns: (keyof typeof CalculationColumn)[] = ['AVRCHM_UNLOAD', 'NPRCH']

      expect(isColumnHidden('AVRCHM_UNLOAD', hiddenColumns)).toBe(true)
      expect(isColumnHidden('NPRCH', hiddenColumns)).toBe(true)
    })

    it('should return false if column is not in hidden list', () => {
      const hiddenColumns: (keyof typeof CalculationColumn)[] = ['AVRCHM_UNLOAD', 'NPRCH']

      expect(isColumnHidden('RESERVES_MAX', hiddenColumns)).toBe(false)
      expect(isColumnHidden('AVRCHM_LOAD', hiddenColumns)).toBe(false)
    })

    it('should return false if hidden list is empty', () => {
      const hiddenColumns: (keyof typeof CalculationColumn)[] = []

      expect(isColumnHidden('AVRCHM_UNLOAD', hiddenColumns)).toBe(false)
      expect(isColumnHidden('NPRCH', hiddenColumns)).toBe(false)
    })
  })

  describe('getDynamicColumnBlockSizes', () => {
    it('should return original sizes when no columns are hidden', () => {
      const hiddenColumns: (keyof typeof CalculationColumn)[] = []
      const result = getDynamicColumnBlockSizes(hiddenColumns)

      expect(result).toEqual(COLUMN_BLOCK_SIZES)
    })

    it('should reduce RESERVES block size when AVRCHM_UNLOAD is hidden', () => {
      const hiddenColumns: (keyof typeof CalculationColumn)[] = ['AVRCHM_UNLOAD']
      const result = getDynamicColumnBlockSizes(hiddenColumns)

      expect(result.RESERVES).toBe(COLUMN_BLOCK_SIZES.RESERVES - 1)
      expect(result.RESULTS).toBe(COLUMN_BLOCK_SIZES.RESULTS)
      expect(result.LIMITS).toBe(COLUMN_BLOCK_SIZES.LIMITS)
    })

    it('should reduce RESERVES block size when NPRCH is hidden', () => {
      const hiddenColumns: (keyof typeof CalculationColumn)[] = ['NPRCH']
      const result = getDynamicColumnBlockSizes(hiddenColumns)

      expect(result.RESERVES).toBe(COLUMN_BLOCK_SIZES.RESERVES - 1)
    })

    it('should reduce RESERVES block size when both AVRCHM_UNLOAD and NPRCH are hidden', () => {
      const hiddenColumns: (keyof typeof CalculationColumn)[] = ['AVRCHM_UNLOAD', 'NPRCH']
      const result = getDynamicColumnBlockSizes(hiddenColumns)

      expect(result.RESERVES).toBe(COLUMN_BLOCK_SIZES.RESERVES - 2)
    })

    it('should reduce MODES block size when MODES_DECLARED is hidden', () => {
      const hiddenColumns: (keyof typeof CalculationColumn)[] = ['MODES_DECLARED']
      const result = getDynamicColumnBlockSizes(hiddenColumns)

      expect(result.MODES).toBe(COLUMN_BLOCK_SIZES.MODES - 1)
      expect(result.RESERVES).toBe(COLUMN_BLOCK_SIZES.RESERVES)
    })
  })

  describe('getDynamicColumnPositions', () => {
    it('should return original positions when no columns are hidden', () => {
      const hiddenColumns: (keyof typeof CalculationColumn)[] = []
      const result = getDynamicColumnPositions(hiddenColumns)

      expect(result.NPRCH).toBe(2) // Исходная позиция NPRCH в блоке RESERVES
    })

    it('should adjust NPRCH position when AVRCHM_UNLOAD is hidden', () => {
      const hiddenColumns: (keyof typeof CalculationColumn)[] = ['AVRCHM_UNLOAD']
      const result = getDynamicColumnPositions(hiddenColumns)

      expect(result.NPRCH).toBe(1) // NPRCH сдвигается на позицию AVRCHM_UNLOAD
    })
  })
})
