import { ICalculationRow, IRguCalculation } from 'entities/api/calculationsManager.entities.ts'
import { CalculationColumn, PlaningStageCategory, PlanningStage, RegistryType } from 'entities/shared/common.entities'
import { IAcceptErrorResponse } from 'entities/store/calculationPageStore.entities'
import { ITempGesCell } from 'entities/widgets/Vault.entities.ts'
import Handsontable from 'handsontable'
import { Plant } from 'pages/CalculationsPage/ui/StationBody/entities'
import { Dispatch, RefObject, SetStateAction } from 'react'
import { getSpreadsheetSelectedCells, SpreadsheetSelectedCells } from 'widgets/Spreadsheet/ui/lib'
import { IInputResultItemProp } from 'widgets/Spreadsheet/ui/Spreadsheet.tsx'

export const isRgu = (key: string) => {
  return key?.split('-')?.length > 1
}

interface objAllowedZones {
  topLine: number
  bottomLine: number
}

// Ключи колонок таблицы Свода
export const keysVault = [
  'P_MIN_RESULT',
  'P_GEN',
  'P_MAX_RESULT',
  'RESERVES_MAX',
  'AVRCHM_LOAD',
  'P_MIN',
  'P_MAX',
  'CM_P_MIN',
  'CM_P_MAX',
  'MODES_P_MIN',
  'MODES_P_MAX',
]

// Ключи колонок таблицы Станции
export const keysStation = [
  'P_MIN_RESULT',
  'P_GEN',
  'P_MAX_RESULT',
  'RESERVES_MAX',
  'AVRCHM_LOAD',
  'NPRCH',
  'P_MIN',
  'P_MAX',
  'CM_P_MIN',
  'CM_P_MAX',
  'MODES_P_MIN',
  'MODES_P_MAX',
  'MODES_DECLARED',
]

export enum typeKeysGes {
  P_MIN_RESULT = 'P_MIN_RESULT',
  P_GEN = 'P_GEN',
  P_MAX_RESULT = 'P_MAX_RESULT',
  RESERVES_MAX = 'RESERVES_MAX',
  AVRCHM_LOAD = 'AVRCHM_LOAD',
  NPRCH = 'NPRCH',
  P_MIN = 'P_MIN',
  P_MAX = 'P_MAX',
  CM_P_MIN = 'CM_P_MIN',
  CM_P_MAX = 'CM_P_MAX',
  MODES_P_MIN = 'MODES_P_MIN',
  MODES_P_MAX = 'MODES_P_MAX',
  MODES_DECLARED = 'MODES_DECLARED',
  CONSUMPT = 'CONSUMPT',
}

type valueValidType = string | number | null | undefined
type finalValueValidType = number | undefined
type typeObjectValidate = string

const checkValue = (value: valueValidType): finalValueValidType => {
  if (typeof value === 'string' && value.trim() === '') return undefined

  const numericValue = Number(value)

  return Number.isNaN(numericValue) ? undefined : +numericValue.toFixed(3)
}

interface validObjectType {
  is_Valid_P_GEN: boolean
  is_Valid_AVRCHM_LOAD: boolean
  is_Valid_P_MIN: boolean
  is_Valid_P_MAX: boolean
  is_Valid_CM_P_MIN: boolean
  is_Valid_CM_P_MAX: boolean
}

interface additionalValidationPlantProps {
  object: ITempGesCell
  value: number
  key: typeKeysGes
  regulatedUnit?: RegistryType
  validObject: validObjectType
}

const additionalValidationPlant_PLANT = () => {
  return undefined
}
const additionalValidationPlant_RGU = (props: additionalValidationPlantProps) => {
  const { key, validObject } = props
  let validateMessage = ``
  switch (key) {
    case typeKeysGes.P_GEN:
      if (validObject.is_Valid_P_GEN) {
        validateMessage = validateMessage + `Сумма Итог(план) РГЕ ≠ Итог (план) станции` + `\n`
      }
      break
    case typeKeysGes.AVRCHM_LOAD:
      if (validObject.is_Valid_AVRCHM_LOAD) {
        validateMessage = validateMessage + `Сумма АВРЧМ РГЕ ≠ АВРЧМ станции` + `\n`
      }
      break
    case typeKeysGes.P_MIN:
      if (validObject.is_Valid_P_MIN) {
        validateMessage = validateMessage + `Сумма Итог.огр(мин) РГЕ > Итог.огр(мин) станции` + `\n`
      }
      break
    case typeKeysGes.P_MAX:
      if (validObject.is_Valid_P_MAX) {
        validateMessage = validateMessage + `Сумма Итог.огр(макс) РГЕ < Итог.огр(макс) станции` + `\n`
      }
      break
    case typeKeysGes.CM_P_MIN:
      if (validObject.is_Valid_CM_P_MIN) {
        validateMessage = validateMessage + `Сумма РМ(мин) РГЕ > РМ(мин) станции` + `\n`
      }
      break
    case typeKeysGes.CM_P_MAX:
      if (validObject.is_Valid_CM_P_MAX) {
        validateMessage = validateMessage + `Сумма РМ(макс) РГЕ < РМ(макс) станции` + `\n`
      }
      break
    default:
      break
  }
  if (validateMessage.length > 0) {
    return validateMessage
  } else {
    return undefined
  }
}
export const additionalValidationPlant = ({ regulatedUnit, ...props }: additionalValidationPlantProps) => {
  switch (regulatedUnit) {
    case 'RGU':
      return additionalValidationPlant_RGU(props)
    case 'PLANT':
      return additionalValidationPlant_PLANT()
    default:
      return undefined
  }
}
export const additionalValidationRgu = (key: typeKeysGes, validObject: validObjectType) => {
  let validateMessage = ``
  switch (key) {
    case typeKeysGes.P_GEN:
      if (validObject.is_Valid_P_GEN) {
        validateMessage = validateMessage + `Сумма Итог(план) РГЕ ≠ Итог (план) станции` + `\n`
      }
      break
    case typeKeysGes.AVRCHM_LOAD:
      if (validObject.is_Valid_AVRCHM_LOAD) {
        validateMessage = validateMessage + `Сумма АВРЧМ РГЕ ≠ АВРЧМ станции` + `\n`
      }
      break
    case typeKeysGes.P_MIN:
      if (validObject.is_Valid_P_MIN) {
        validateMessage = validateMessage + `Сумма Итог.огр(мин) РГЕ > Итог.огр(мин) станции` + `\n`
      }
      break
    case typeKeysGes.P_MAX:
      if (validObject.is_Valid_P_MAX) {
        validateMessage = validateMessage + `Сумма Итог.огр(макс) РГЕ < Итог.огр(макс) станции` + `\n`
      }
      break
    case typeKeysGes.CM_P_MIN:
      if (validObject.is_Valid_CM_P_MIN) {
        validateMessage = validateMessage + `Сумма РМ(мин) РГЕ > РМ(мин) станции` + `\n`
      }
      break
    case typeKeysGes.CM_P_MAX:
      if (validObject.is_Valid_CM_P_MAX) {
        validateMessage = validateMessage + `Сумма РМ(макс) РГЕ < РМ(макс) станции` + `\n`
      }
      break
    default:
      break
  }
  if (validateMessage.length > 0) {
    return validateMessage
  } else {
    return undefined
  }
}
export const additionalValidation = (
  type: typeObjectValidate,
  object: ITempGesCell,
  value: valueValidType,
  key: typeKeysGes,
  regulatedUnit: RegistryType | undefined,
  validObject: validObjectType,
) => {
  const finValue = checkValue(value)
  if (finValue !== undefined) {
    switch (type) {
      case 'PLANT':
        return additionalValidationPlant({
          object,
          value: finValue,
          key,
          regulatedUnit,
          validObject,
        })
      case 'RGU':
        return additionalValidationRgu(key, validObject)
      default:
        return undefined
    }
  } else {
    return undefined
  }
}

export const getStatusCell = (
  isMaxConsumptionHour: boolean,
  isMinConsumptionHour: boolean,
  editor: Handsontable.GridSettings['editor'],
  keyStation: string,
  type: string,
  isValid: boolean,
  hasManualAdjustments?: boolean,
) => {
  const plantKeyData = keyStation.split('-')
  const plantKey = plantKeyData[1] ?? plantKeyData[0]
  const classes = [
    {
      // type содержит 'plantOptimized' или 'plantNotOptimized'
      className: type,
      enabled: plantKey === typeKeysGes.P_GEN && type.includes('plantNotOptimized'),
    },
    {
      // type содержит 'plantOptimized' или 'plantNotOptimized'
      className: type,
      enabled:
        (plantKey === typeKeysGes.P_MIN_RESULT || plantKey === typeKeysGes.P_MAX_RESULT) &&
        type.includes('plantOptimized'),
    },
    {
      className: 'borderLeft',
      enabled: plantKey === typeKeysGes.P_MIN_RESULT && type === 'plantOptimized',
    },
    {
      className: 'plantLimit',
      enabled: plantKey === typeKeysGes.P_MIN || plantKey === typeKeysGes.P_MAX,
    },
    {
      className: 'plantLimitRgu',
      enabled: (plantKey === typeKeysGes.P_MIN || plantKey === typeKeysGes.P_MAX) && plantKeyData.length > 1,
    },
    {
      className: 'plantModes',
      enabled:
        plantKey === typeKeysGes.MODES_P_MIN ||
        plantKey === typeKeysGes.MODES_P_MAX ||
        plantKey === typeKeysGes.MODES_DECLARED,
    },
    {
      className: 'isNotValid',
      enabled: !isValid,
    },
    {
      className: 'minConsumptionHour',
      enabled: !(isMaxConsumptionHour && isMinConsumptionHour) && isMinConsumptionHour,
    },
    {
      className: 'maxConsumptionHour',
      enabled: (isMaxConsumptionHour && isMinConsumptionHour) || isMaxConsumptionHour,
    },
    {
      className: 'disabledCell',
      enabled: typeof editor !== 'string',
    },
    {
      className: 'manualAdjustments',
      enabled: hasManualAdjustments,
    },
  ]

  return classes
    .filter((classConfig) => classConfig.enabled)
    .map((classConfig) => classConfig.className)
    .join(' ')
}

export const updateManualAdjustmentStatusForCell = (className: string, hasManualAdjustments?: boolean) => {
  let manualAdjustments: string = ``
  if (hasManualAdjustments && !className.includes('manualAdjustments')) {
    manualAdjustments = 'manualAdjustments'
  } else if (!hasManualAdjustments) {
    return className.replace('manualAdjustments', '')
  }

  return `${className} ${manualAdjustments}`
}

const prepareToFixedValue = (value: string | number | null | undefined) => {
  return Number(Number(value).toFixed(3))
}

export const getValidGES = (
  _: number | null,
  key: string,
  object: ITempGesCell,
  value: valueValidType,
  inputResultProps?: IInputResultItemProp,
): string | undefined => {
  let validateMessage = ``
  const finValue = checkValue(value)
  if (finValue !== undefined) {
    if (key === 'P_MIN_RESULT') {
      const allowedZones = object?.allowedZones ?? []
      if (finValue < 0) {
        validateMessage = validateMessage + `Итог(мин) < 0` + `\n`
      }
      if (finValue > prepareToFixedValue(object[`P_MAX_RESULT`])) {
        validateMessage = validateMessage + `Итог(мин) > Итог(макс)` + `\n`
      }
      if (finValue > prepareToFixedValue(object[`P_GEN`])) {
        validateMessage = validateMessage + `Итог(мин) > Итог(план)` + `\n`
      }
      if (allowedZones.length > 0) {
        const isAllowedZones =
          allowedZones.length > 0
            ? !!object?.allowedZones?.some((el: objAllowedZones) => finValue >= el.bottomLine && finValue <= el.topLine)
            : false
        if (!isAllowedZones) {
          validateMessage = validateMessage + `Итог(мин) вне доп. зон` + `\n`
        }
      }
      if (
        inputResultProps &&
        inputResultProps.wMax.value !== '' &&
        Number(inputResultProps.wMax.value) === 0 &&
        finValue !== 0
      ) {
        validateMessage += 'Если Эмакс = 0, то Эмин и все значения Итог(макс), Итог(мин) и АВРЧМ должны быть равны 0\n'
      }
    }
    if (key === 'P_GEN') {
      const allowedZones = object?.allowedZones ?? []
      if (finValue < 0) {
        validateMessage = validateMessage + `Итог(план) < 0` + `\n`
      }
      if (finValue < prepareToFixedValue(object[`P_MIN_RESULT`])) {
        validateMessage = validateMessage + `Итог(план) < Итог(мин)` + `\n`
      }
      if (finValue > prepareToFixedValue(object[`P_MAX_RESULT`])) {
        validateMessage = validateMessage + `Итог(план) > Итог(макс)` + `\n`
      }
      if (allowedZones.length > 0) {
        const isAllowedZones =
          allowedZones.length > 0
            ? !!object?.allowedZones?.some((el: objAllowedZones) => finValue >= el.bottomLine && finValue <= el.topLine)
            : false
        if (!isAllowedZones) {
          validateMessage = validateMessage + `Итог(план) вне доп. зон` + `\n`
        }
      }
      if (
        inputResultProps &&
        inputResultProps.pGen.value !== '' &&
        Number(inputResultProps.pGen.value) === 0 &&
        finValue !== 0
      ) {
        validateMessage += 'Если Эплан = 0, то все значения Итог(план) и АВРЧМ должны быть равны 0\n'
      }
    }
    if (key === 'P_MAX_RESULT') {
      const allowedZones = object?.allowedZones ?? []
      if (finValue < 0) {
        validateMessage = validateMessage + `Итог(макс) < 0` + `\n`
      }
      if (finValue < prepareToFixedValue(object[`P_MIN_RESULT`])) {
        validateMessage = validateMessage + `Итог(макс) < Итог(мин)` + `\n`
      }
      if (finValue < prepareToFixedValue(object[`P_GEN`])) {
        validateMessage = validateMessage + `Итог(макс) < Итог(план)` + `\n`
      }
      if (allowedZones.length > 0) {
        const isAllowedZones =
          allowedZones.length > 0
            ? !!object?.allowedZones?.some((el: objAllowedZones) => finValue >= el.bottomLine && finValue <= el.topLine)
            : false
        if (!isAllowedZones) {
          validateMessage = validateMessage + `Итог(макс) вне доп. зон` + `\n`
        }
      }
      if (
        inputResultProps &&
        inputResultProps.wMax.value !== '' &&
        Number(inputResultProps.wMax.value) === 0 &&
        finValue !== 0
      ) {
        validateMessage += 'Если Эмакс = 0, то Эмин и все значения Итог(макс), Итог(мин) и АВРЧМ должны быть равны 0\n'
      }
    }
    if (key === 'RESERVES_MAX') {
      if (finValue < 0) {
        validateMessage = validateMessage + `Rмакс < 0` + `\n`
      }
      if (finValue < prepareToFixedValue(object[`AVRCHM_LOAD`])) {
        validateMessage = validateMessage + `Rмакс < АВРЧМ` + `\n`
      }
    }
    if (key === 'AVRCHM_LOAD') {
      if (finValue < 0) {
        validateMessage = validateMessage + `АВРЧМ < 0` + `\n`
      }
      if (finValue > prepareToFixedValue(object[`RESERVES_MAX`])) {
        validateMessage = validateMessage + `АВРЧМ > Rмакс ` + `\n`
      }
      if (
        inputResultProps &&
        inputResultProps.pGen.value !== '' &&
        Number(inputResultProps.pGen.value) === 0 &&
        finValue !== 0
      ) {
        validateMessage += 'Если Эплан = 0, то все значения Итог(план) и АВРЧМ должны быть равны 0\n'
      }
      if (
        inputResultProps &&
        inputResultProps.wMax.value !== '' &&
        Number(inputResultProps.wMax.value) === 0 &&
        finValue !== 0
      ) {
        validateMessage += 'Если Эмакс = 0, то Эмин и все значения Итог(макс), Итог(мин) и АВРЧМ должны быть равны 0\n'
      }
    }
    if (key === 'P_MIN') {
      if (finValue < 0) {
        validateMessage = validateMessage + `Итог.огр(мин) < 0` + `\n`
      }
      if (finValue > prepareToFixedValue(object[`P_MAX`])) {
        validateMessage = validateMessage + `Итог.огр(мин) > Итог.огр(макс)` + `\n`
      }
    }
    if (key === 'P_MAX') {
      if (finValue < 0) {
        validateMessage = validateMessage + `Итог.огр(макс) < 0` + `\n`
      }
      if (finValue < prepareToFixedValue(object[`P_MIN`])) {
        validateMessage = validateMessage + `Итог.огр(макс) < Итог.огр(мин)` + `\n`
      }
    }
    if (key === 'CM_P_MIN' && prepareToFixedValue(object[`CM_P_MAX`]) !== undefined) {
      if (finValue < 0) {
        validateMessage = validateMessage + `РМ(мин) < 0` + `\n`
      }
      if (finValue > prepareToFixedValue(object[`CM_P_MAX`])) {
        validateMessage = validateMessage + `РМ(мин) > РМ(макс)` + `\n`
      }
    }
    if (key === 'CM_P_MAX' && object[`CM_P_MIN`] !== undefined) {
      if (finValue < 0) {
        validateMessage = validateMessage + `РМ(макс) < 0` + `\n`
      }
      if (finValue < prepareToFixedValue(object[`CM_P_MIN`])) {
        validateMessage = validateMessage + `РМ(макс) < РМ(мин)` + `\n`
      }
    }
    if (key === 'MODES_P_MIN' && object[`MODES_P_MAX`] !== undefined) {
      if (finValue < 0) {
        validateMessage = validateMessage + `Модес(мин) < 0` + `\n`
      }
      if (finValue > prepareToFixedValue(object[`MODES_P_MAX`])) {
        validateMessage = validateMessage + `Модес(мин) > Модес(макс)` + `\n`
      }
    }
    if (key === 'MODES_P_MAX' && object[`MODES_P_MIN`] !== undefined) {
      if (finValue < 0) {
        validateMessage = validateMessage + `Модес(макс) < 0` + `\n`
      }
      if (finValue < prepareToFixedValue(object[`MODES_P_MIN`])) {
        validateMessage = validateMessage + `Модес(макс) < Модес(мин)` + `\n`
      }
    }
    if (key === 'CONSUMPT') {
      if (finValue < 0) {
        validateMessage = validateMessage + `Потр < 0` + `\n`
      }
    }
  }
  if (validateMessage.length > 0) {
    return validateMessage
  } else {
    return undefined
  }
}

export const ACTUAL_ITEM = {
  value: 'ACTUAL',
  label: 'Актуальный',
  color: '#000000',
}

export const hours = new Array(24).fill(null).map((_, index) => String(index + 1))

export const getPrepareDate = (date: Date) => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1 > 9 ? date.getMonth() + 1 : `0${date.getMonth() + 1}`
  const day = date.getDate() > 9 ? date.getDate() : `0${date.getDate()}`

  return `${year}-${month}-${day}`
}

export const downloadTheSourceData = (
  type: string,
  selectedStage: string,
  actualStage: { code: number | string },
  date: Date,
  selectLeftMenu: number,
  onDownloadTheSourceData: (
    selectLeftMenu: number,
    date: string,
    type: string,
    planingStage: string | number,
    dateISP: string,
  ) => void,
  dateISP: string,
) => {
  const planingStage = selectedStage === ACTUAL_ITEM.value ? actualStage?.code : selectedStage
  if (type === 'LOAD_PLANT_DATA') {
    const prepareDate = getPrepareDate(date)
    onDownloadTheSourceData(selectLeftMenu, prepareDate, 'LOAD_PLANT_DATA', planingStage, dateISP)
    onDownloadTheSourceData(selectLeftMenu, prepareDate, 'LOAD_GENERATOR_ALLOWED_ZONES', planingStage, dateISP)
  } else {
    const prepareDate = getPrepareDate(date)
    onDownloadTheSourceData(selectLeftMenu, prepareDate, type, planingStage, dateISP)
  }
}

export const handleAcceptObject = async (
  date: Date,
  selectedStage: string,
  setAcceptObject: (selectLeftMenu: number, date: string, selectedStage: string, forced: boolean) => Promise<boolean>,
  selectLeftMenu: number,
  setIsAccepted: Dispatch<SetStateAction<boolean | null>>,
  initLoadData: VoidFunction,
  forced: boolean,
  setData: (data: Plant[]) => void,
  data: Plant[],
) => {
  const prepareDate = getPrepareDate(date)

  try {
    await setAcceptObject(selectLeftMenu, prepareDate, selectedStage, forced)

    initLoadData()
    setData(data.map((el) => (el.plantId === selectLeftMenu ? { ...el, accepted: true } : el)))

    setIsAccepted(null)
  } catch (e) {
    const error = e as IAcceptErrorResponse

    if (error.status === 400) {
      setIsAccepted(null) // Скрывает AcceptModal только в случае 400 ошибки
    } else {
      console.error('Ошибка в handleAcceptObject:', e)
    }

    throw e
  }
}

export const handleDisacceptObject = async (
  date: Date,
  selectedStage: string,
  selectLeftMenu: number,
  setDisacceptObject: (select: number, date: string, stage: string) => Promise<void>,
  setIsAccepted: Dispatch<SetStateAction<boolean | null>>,
  initLoadData: VoidFunction,
  setData: (data: Plant[]) => void,
  data: Plant[],
) => {
  const prepareDate = getPrepareDate(date)

  try {
    await setDisacceptObject(selectLeftMenu, prepareDate, selectedStage)
    initLoadData()
    setData(data.map((el) => (el.plantId === selectLeftMenu ? { ...el, accepted: false } : el)))
    setIsAccepted(null)
  } catch (error) {
    console.error('Ошибка в handleDisacceptObject:', error)
    throw error
  }
}

export const resizeWebUp = (
  refUp: RefObject<HTMLDivElement>,
  setHeightUp: Dispatch<SetStateAction<number | undefined>>,
  setWidthUp: Dispatch<SetStateAction<number | undefined>>,
) => {
  if (refUp) {
    const curHeight = refUp?.current?.getBoundingClientRect()?.height
    const curWidth = refUp?.current?.getBoundingClientRect()?.width
    setHeightUp(curHeight)
    setWidthUp(curWidth)
  }
}

export const resize = (
  refUp: RefObject<HTMLDivElement>,
  setHeightUp: Dispatch<SetStateAction<number | undefined>>,
  setWidthUp: Dispatch<SetStateAction<number | undefined>>,
) => {
  resizeWebUp(refUp, setHeightUp, setWidthUp)
}

export const getColorStage = (key: PlanningStage, finished: boolean) => {
  if (finished) {
    return 'planningStagesDoNotMatchHeaderLabel'
  }
  if (key === 'PER' || key === 'PDG') {
    return 'RSVHeaderLabel'
  }
  if (key === 'VSVGO1' || key === 'VSVGO2' || key === 'VSVGO3') {
    return 'VSVGOHeaderLabel'
  }

  return ''
}

export const getColorByCategory = (category?: PlaningStageCategory, corresponds?: boolean) => {
  if (!corresponds) {
    return 'planningStagesDoNotMatchHeaderLabel'
  }
  if (category === PlaningStageCategory.RSV) {
    return 'RSVHeaderLabel'
  }
  if (category === PlaningStageCategory.VSVGO) {
    return 'VSVGOHeaderLabel'
  }

  return ''
}

// Структура блоков колонок в таблице Свода
const gesVaultBlockByLevels = [
  ['P_MIN_RESULT', 'P_GEN', 'P_MAX_RESULT'],
  ['RESERVES_MAX', 'AVRCHM_LOAD'],
  ['LIMIT_MIN', 'LIMIT_MAX'],
  ['CM_P_MIN', 'CM_P_MAX'],
  ['MODES_P_MIN', 'MODES_P_MAX'],
]

// Структура блоков колонок в таблице Станции
const gesStationBlockByLevels = [
  ['P_MIN_RESULT', 'P_GEN', 'P_MAX_RESULT'],
  ['RESERVES_MAX', 'AVRCHM_LOAD', 'NPRCH'],
  ['LIMIT_MIN', 'LIMIT_MAX'],
  ['CM_P_MIN', 'CM_P_MAX'],
  ['MODES_P_MIN', 'MODES_P_MAX', 'MODES_DECLARED'],
]

// Находит объект со свойствами ячейки в исходных данных по её "физическим" координатам в таблице Свода
// TODO: Провести рефакторинг с целью убрать хардкод (см. реализацию getGesCellPropByTableCoords для Станции).
export const getVaultCellPropByTableCoords = (
  rows: ICalculationRow[],
  rgus: IRguCalculation[],
  rowId: number,
  colId: number,
) => {
  const stationDataForHour = rows[rowId]
  const rguLength = rgus.length
  const resulColumnLength = 3 * (rguLength + 1)
  const baseColumnLength = 2 * (rguLength + 1)
  let changedColNumInFirstLevel = 0
  let changedColNumInSecondLevel = colId
  if (colId >= resulColumnLength + baseColumnLength * 4) return

  while (
    (changedColNumInSecondLevel >= resulColumnLength && changedColNumInFirstLevel === 0) ||
    (changedColNumInSecondLevel >= baseColumnLength && changedColNumInFirstLevel > 0)
  ) {
    if (changedColNumInFirstLevel === 0) {
      changedColNumInSecondLevel -= resulColumnLength
    } else {
      changedColNumInSecondLevel -= baseColumnLength
    }
    changedColNumInFirstLevel++
  }
  // Индекс указывающий на то, где произошло изменения: в станции или по одной из РГЕ
  let changedBlock = Math.ceil(changedColNumInSecondLevel / gesVaultBlockByLevels[changedColNumInFirstLevel].length) - 1
  // Индекс указывающий на то, какая именно колонка изменилась
  const colNumInsideChangedBlock = changedColNumInSecondLevel % gesVaultBlockByLevels[changedColNumInFirstLevel].length
  if (
    colNumInsideChangedBlock === 0 &&
    ((changedColNumInFirstLevel === 0 && changedColNumInSecondLevel < resulColumnLength) ||
      (changedColNumInFirstLevel > 0 && changedColNumInSecondLevel < baseColumnLength))
  ) {
    changedBlock++
  }

  if (changedBlock <= 0) {
    return stationDataForHour.cells.find(
      (cell) => cell.column === gesStationBlockByLevels[changedColNumInFirstLevel][colNumInsideChangedBlock],
    )
  } else {
    return rgus[changedBlock - 1].rows[rowId].cells.find(
      (cell) => cell.column === gesStationBlockByLevels[changedColNumInFirstLevel][colNumInsideChangedBlock],
    )
  }
}

// Константы для расчета индексов колонок
export const COLUMN_BLOCK_SIZES = {
  RESULTS: 3, // P_MIN_RESULT, P_GEN, P_MAX_RESULT
  RESERVES: 3, // RESERVES_MAX, AVRCHM_LOAD, NPRCH
  LIMITS: 2, // P_MIN, P_MAX
  CM: 2, // CM_P_MIN, CM_P_MAX
  MODES: 3, // MODES_P_MIN, MODES_P_MAX, MODES_DECLARED
  CONSUMPT: 1, // CONSUMPT
} as const

// Позиции колонок в блоках
export const COLUMN_POSITIONS = {
  // Блок "Итог" (0-2)
  P_MIN_RESULT: 0,
  P_GEN: 1,
  P_MAX_RESULT: 2,

  // Блок "Резервы" (0-2)
  RESERVES_MAX: 0,
  AVRCHM_LOAD: 1,
  NPRCH: 2,

  // Блок "Итог.ОГР" (0-1)
  P_MIN: 0,
  P_MAX: 1,

  // Блок "РМ" (0-1)
  CM_P_MIN: 0,
  CM_P_MAX: 1,

  // Блок "Модес" (0-2)
  MODES_P_MIN: 0,
  MODES_P_MAX: 1,
  MODES_DECLARED: 2,

  // Блок "ИСП" (0)
  CONSUMPT: 0,
} as const

/**
 * Вычисляет индекс колонки для станции в таблице
 *
 * Структура таблицы:
 * 1. Блок RESULTS (3 колонки на Σ + 3 на каждую РГЕ): P_MIN_RESULT, P_GEN, P_MAX_RESULT
 * 2. Блок RESERVES (3 колонки на Σ + 3 на каждую РГЕ): RESERVES_MAX, AVRCHM_LOAD, NPRCH
 * 3. Блок LIMITS (2 колонки на Σ + 2 на каждую РГЕ): P_MIN, P_MAX
 * 4. Блок CM (2 колонки на Σ + 2 на каждую РГЕ): CM_P_MIN, CM_P_MAX
 * 5. Блок MODES (3 колонки на Σ + 3 на каждую РГЕ): MODES_P_MIN, MODES_P_MAX, MODES_DECLARED
 * 6. Блок CONSUMPT (1 колонка только в конце таблицы): CONSUMPT
 *
 * Каждый блок начинается с колонок Σ, затем идут колонки РГЕ.
 * CONSUMPT - особый случай, не является частью стандартных блоков, стоит особняком в конце таблицы
 *
 * @param blockType - тип блока (RESULTS, RESERVES, LIMITS, CM, MODES, CONSUMPT)
 * @param columnPosition - позиция колонки в блоке (0, 1, 2...)
 * @param rguCount - количество РГЕ
 * @param dynamicBlockSizes - динамические размеры блоков с учетом скрытых колонок
 * @returns индекс колонки в таблице (0-based)
 */
export const getStationColumnIndex = (
  blockType: keyof typeof COLUMN_BLOCK_SIZES,
  columnPosition: number,
  rguCount: number,
  dynamicBlockSizes: typeof COLUMN_BLOCK_SIZES = COLUMN_BLOCK_SIZES,
): number => {
  let baseIndex = 0

  // Добавляем размеры предыдущих блоков
  if (blockType === 'RESERVES') {
    baseIndex += dynamicBlockSizes.RESULTS * (rguCount + 1) // Блок "Итог"
  } else if (blockType === 'LIMITS') {
    baseIndex += dynamicBlockSizes.RESULTS * (rguCount + 1) // Блок "Итог"
    baseIndex += dynamicBlockSizes.RESERVES * (rguCount + 1) // Блок "Резервы"
  } else if (blockType === 'CM') {
    baseIndex += dynamicBlockSizes.RESULTS * (rguCount + 1) // Блок "Итог"
    baseIndex += dynamicBlockSizes.RESERVES * (rguCount + 1) // Блок "Резервы"
    baseIndex += dynamicBlockSizes.LIMITS * (rguCount + 1) // Блок ограничений
  } else if (blockType === 'MODES') {
    baseIndex += dynamicBlockSizes.RESULTS * (rguCount + 1) // Блок "Итог"
    baseIndex += dynamicBlockSizes.RESERVES * (rguCount + 1) // Блок "Резервы"
    baseIndex += dynamicBlockSizes.LIMITS * (rguCount + 1) // Блок "Итог.ОГР"
    baseIndex += dynamicBlockSizes.CM * (rguCount + 1) // Блок РМ
  } else if (blockType === 'CONSUMPT') {
    baseIndex += dynamicBlockSizes.RESULTS * (rguCount + 1) // Блок "Итог"
    baseIndex += dynamicBlockSizes.RESERVES * (rguCount + 1) // Блок "Резервы"
    baseIndex += dynamicBlockSizes.LIMITS * (rguCount + 1) // Блок "Итог.ОГР"
    baseIndex += dynamicBlockSizes.CM * (rguCount + 1) // Блок РМ
    baseIndex += dynamicBlockSizes.MODES * (rguCount + 1) // Блок "Модес"
  }

  return baseIndex + columnPosition
}

/**
 * Вычисляет индекс колонки для конкретной РГЕ в таблице станции
 *
 * Пример для 2 РГЕ в блоке RESULTS:
 * Колонки: [Σ P_MIN_RESULT, Σ P_GEN, Σ P_MAX_RESULT,
 *           РГЕ1 P_MIN_RESULT, РГЕ1 P_GEN, РГЕ1 P_MAX_RESULT,
 *           РГЕ2 P_MIN_RESULT, РГЕ2 P_GEN, РГЕ2 P_MAX_RESULT]
 * Индексы: [0, 1, 2, 3, 4, 5, 6, 7, 8]
 *
 * Для получения РГЕ1 P_GEN: getRguColumnIndex('RESULTS', 1, 0, 2) = 4
 * Для получения РГЕ2 P_MAX_RESULT: getRguColumnIndex('RESULTS', 2, 1, 2) = 8
 *
 * @param blockType - тип блока (RESULTS, RESERVES, LIMITS, CM, MODES)
 * @param columnPosition - позиция колонки в блоке (0=первая, 1=вторая, 2=третья)
 * @param rguIndex - индекс РГЕ (0=первая РГЕ, 1=вторая РГЕ, ...)
 * @param rguCount - общее количество РГЕ
 * @param dynamicBlockSizes - динамические размеры блоков с учетом скрытых колонок
 * @returns индекс колонки в таблице (0-based)
 */
export const getRguColumnIndex = (
  blockType: keyof typeof COLUMN_BLOCK_SIZES,
  columnPosition: number,
  rguIndex: number,
  rguCount: number,
  dynamicBlockSizes: typeof COLUMN_BLOCK_SIZES = COLUMN_BLOCK_SIZES,
): number => {
  const stationBlockStartIndex = getStationColumnIndex(blockType, 0, rguCount, dynamicBlockSizes)
  const blockSize = dynamicBlockSizes[blockType]

  // Σ занимает первые blockSize колонок в блоке, затем идут РГЕ
  // Формула: начало блока + размер станции + (индекс РГЕ * размер блока) + позиция в блоке
  return stationBlockStartIndex + blockSize + rguIndex * blockSize + columnPosition
}

/**
 * Находит объект со свойствами ячейки в исходных данных по её "физическим" координатам в таблице.
 *
 * Эта функция выполняет обратное преобразование по отношению к `getStationColumnIndex` и `getRguColumnIndex`.
 * Если те функции по логическому имени (например, "P_GEN для РГУ-1") находят физический индекс колонки,
 * то эта функция по физическому индексу колонки (`colId`) определяет, к какому логическому
 * параметру и какому объекту (станция или конкретная РГУ) эта колонка относится.
 *
 * Это необходимо для получения полных данных ячейки (например, флагов `manual`, `fixed`
 * и др.) при обработке событий в таблице (клик, редактирование).
 *
 * @param rows - Массив данных по строкам (часам).
 * @param rgus - Массив данных для каждой РГЕ.
 * @param rowId - Индекс строки в таблице (соответствует часу, 0-23).
 * @param colId - "Физический" индекс колонки в таблице, начиная с 0.
 * @returns Объект ячейки из исходной структуры данных (`{ column, value, manual, ... }`) или undefined, если не найден.
 */
export const getGesCellPropByTableCoords = (
  rows: ICalculationRow[],
  rgus: IRguCalculation[],
  rowId: number,
  colId: number,
) => {
  // Получаем данные по станции для конкретного часа (строки)
  const stationDataForHour = rows[rowId]
  const rguLength = rgus.length

  // --- Шаг 1: Рассчитываем общую ширину каждого большого блока колонок (например, "Итог", "Резервы") ---

  // Ширина блока = (кол-во колонок для одного объекта) * (кол-во РГУ + 1 (для станции/Σ))
  const resulColumnLength = COLUMN_BLOCK_SIZES.RESULTS * (rguLength + 1)
  const reservesColumnLength = COLUMN_BLOCK_SIZES.RESERVES * (rguLength + 1)
  const limitsColumnLength = COLUMN_BLOCK_SIZES.LIMITS * (rguLength + 1)
  const cmColumnLength = COLUMN_BLOCK_SIZES.CM * (rguLength + 1)
  const modesColumnLength = COLUMN_BLOCK_SIZES.MODES * (rguLength + 1)
  // Быстрая проверка на выход за пределы всех известных блоков
  if (colId >= resulColumnLength + reservesColumnLength + limitsColumnLength + cmColumnLength + modesColumnLength)
    return

  // --- Шаг 2: Определяем, в каком из больших блоков (`RESULTS`, `RESERVES` и т.д.) находится колонка ---

  // `changedColNumInFirstLevel` - это индекс большого блока (0: RESULTS, 1: RESERVES, и т.д.).
  // Он соответствует индексу в массиве `gesStationBlockByLevels`.
  let changedColNumInFirstLevel = 0

  // `changedColNumInSecondLevel` - это индекс колонки относительно начала её большого блока.
  // Мы начинаем с абсолютного индекса и вычитаем ширину предыдущих блоков.
  let changedColNumInSecondLevel = colId

  // Создаем массив с ширинами всех блоков для удобной итерации в цикле.
  const blockLengths = [resulColumnLength, reservesColumnLength, limitsColumnLength, cmColumnLength, modesColumnLength]

  // Цикл "перешагивает" через большие блоки, пока не найдет тот, в котором находится `colId`.
  while (
    changedColNumInFirstLevel < blockLengths.length &&
    changedColNumInSecondLevel >= blockLengths[changedColNumInFirstLevel]
  ) {
    // Вычитаем ширину пройденного блока из индекса колонки
    changedColNumInSecondLevel -= blockLengths[changedColNumInFirstLevel]
    // Переходим к следующему большому блоку
    changedColNumInFirstLevel++
  }

  // Если после цикла мы вышли за пределы известных блоков, что-то пошло не так, возвращаем undefined
  if (changedColNumInFirstLevel >= blockLengths.length) return

  // --- Шаг 3: Определяем объект (Станция или РГЕ) и конкретную колонку внутри него ---

  // `columnsInBlock` - это массив названий колонок для текущего большого блока,
  // например, для RESULTS это будет ['P_MIN_RESULT', 'P_GEN', 'P_MAX_RESULT'].
  const columnsInBlock = gesStationBlockByLevels[changedColNumInFirstLevel]

  // `changedBlock` - индекс под-блока внутри большого блока. 0 - для станции (Σ), 1 - для РГЕ-1, 2 - для РГЕ-2 и т.д.
  // Мы делим локальный индекс колонки на количество колонок для одного объекта.
  // `Math.ceil(...) - 1` корректно обрабатывает группы колонок.
  // Пример: для блока из 3 колонок, индексы 0,1,2 дадут 0; индексы 3,4,5 дадут 1.
  let changedBlock = Math.ceil(changedColNumInSecondLevel / columnsInBlock.length) - 1

  // `colNumInsideChangedBlock` - это финальный индекс колонки внутри своего под-блока (0, 1 или 2).
  // Например, для `P_GEN` в блоке `RESULTS` это будет 1.
  const colNumInsideChangedBlock = changedColNumInSecondLevel % columnsInBlock.length

  // Корректировка для граничных случаев. Когда `changedColNumInSecondLevel` является
  // точной границей между под-блоками (например, 3 для блока размером 3),
  // `Math.ceil` может дать неверный результат. Этот `if` исправляет его.
  if (colNumInsideChangedBlock === 0 && changedColNumInSecondLevel < blockLengths[changedColNumInFirstLevel]) {
    changedBlock++
  }

  // --- Шаг 4: Возвращаем нужную ячейку из исходной структуры данных ---

  // Если `changedBlock` <= 0, значит, колонка относится к данным станции (Σ).
  if (changedBlock <= 0) {
    return stationDataForHour.cells.find((cell) => cell.column === columnsInBlock[colNumInsideChangedBlock])
  } else {
    // Иначе колонка относится к одной из РГЕ.
    // `changedBlock - 1` используется для получения правильного индекса в массиве `rgus`,
    // так как `changedBlock=1` соответствует `rgus[0]`.
    return rgus[changedBlock - 1].rows[rowId].cells.find(
      (cell) => cell.column === columnsInBlock[colNumInsideChangedBlock],
    )
  }
}

export const handleSelectedCoords = (
  hot: Handsontable | null,
  setSelectedCells: (cells: SpreadsheetSelectedCells[]) => void,
  setOutsideTableClickCounter: (outsideTableClickCounter: number) => void,
) => {
  const transformedSelectedRange = getSpreadsheetSelectedCells(hot)
  setSelectedCells(transformedSelectedRange)
  setOutsideTableClickCounter(0)
}

export const getStyledComment = (value: string | undefined, readOnly = true) => {
  if (!value) return { value, readOnly }

  // Константы для расчёта ширины и высоты комментария
  const CHAR_WIDTH = 6.6
  const CHAR_W_PADDING = 16
  const CHAR_HEIGHT = 15
  const CHAR_H_PADDING = 10

  const trimmedValue = value.trim()
  const lines = trimmedValue.split('\n')
  const maxLineLength = Math.max(...lines.map((line) => line.length))

  // Динамически вычисляем ширину и высоту комментария по содержимому
  const style = {
    width: CHAR_WIDTH * maxLineLength + CHAR_W_PADDING,
    height: CHAR_HEIGHT * lines.length + CHAR_H_PADDING,
  }

  return { value: trimmedValue, readOnly, style }
}

/**
 * Проверяет, должна ли колонка быть скрыта на основе параметра HIDDEN_COLUMNS
 * @param columnKey - ключ колонки (например, 'AVRCHM_UNLOAD', 'NPRCH')
 * @param hiddenColumns - массив скрытых колонок из plantHiddenColumns
 * @returns true, если колонка должна быть скрыта
 */
export const isColumnHidden = (
  columnKey: keyof typeof CalculationColumn,
  hiddenColumns: (keyof typeof CalculationColumn)[] = [],
): boolean => {
  return hiddenColumns.includes(columnKey)
}

/**
 * Создает динамические размеры блоков с учетом скрытых колонок
 * @param hiddenColumns - массив скрытых колонок из plantHiddenColumns
 * @returns объект с размерами блоков
 */
export const getDynamicColumnBlockSizes = (hiddenColumns: (keyof typeof CalculationColumn)[] = []) => {
  const baseBlockSizes = { ...COLUMN_BLOCK_SIZES }

  // Проверяем скрытые колонки в блоке RESERVES
  if (isColumnHidden('AVRCHM_UNLOAD', hiddenColumns)) {
    baseBlockSizes.RESERVES -= 1
  }
  if (isColumnHidden('NPRCH', hiddenColumns)) {
    baseBlockSizes.RESERVES -= 1
  }

  // Проверяем скрытые колонки в блоке MODES
  if (isColumnHidden('MODES_DECLARED', hiddenColumns)) {
    baseBlockSizes.MODES -= 1
  }

  return baseBlockSizes
}

/**
 * Создает динамические позиции колонок с учетом скрытых колонок
 * @param hiddenColumns - массив скрытых колонок из plantHiddenColumns
 * @returns объект с позициями колонок
 */
export const getDynamicColumnPositions = (hiddenColumns: (keyof typeof CalculationColumn)[] = []) => {
  const basePositions = { ...COLUMN_POSITIONS } as Record<string, number>

  // Корректируем позиции в блоке RESERVES
  if (isColumnHidden('AVRCHM_UNLOAD', hiddenColumns)) {
    // Если AVRCHM_UNLOAD скрыта, NPRCH сдвигается на позицию 1
    if (!isColumnHidden('NPRCH', hiddenColumns)) {
      basePositions.NPRCH = 1
    }
  }

  // Корректируем позиции в блоке MODES
  if (isColumnHidden('MODES_DECLARED', hiddenColumns)) {
    // Если MODES_DECLARED скрыта, блок становится размером 2 вместо 3
    // Позиции MODES_P_MIN и MODES_P_MAX остаются 0 и 1 соответственно
  }

  return basePositions
}

/**
 * Фильтрует массив колонок, исключая скрытые
 * @param columns - исходный массив колонок
 * @param hiddenColumns - массив скрытых колонок
 * @returns отфильтрованный массив колонок
 */
export const filterVisibleColumns = <T>(
  columns: T[],
  hiddenColumns: (keyof typeof CalculationColumn)[] = [],
  columnKeys: (keyof typeof CalculationColumn)[] = [],
): T[] => {
  if (hiddenColumns.length === 0 || columnKeys.length === 0) {
    return columns
  }

  return columns.filter((_, index) => {
    const columnKey = columnKeys[index]
    return columnKey ? !isColumnHidden(columnKey, hiddenColumns) : true
  })
}
