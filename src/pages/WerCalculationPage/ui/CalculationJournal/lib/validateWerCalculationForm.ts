import { format, isAfter, isBefore, isValid, startOfDay } from 'date-fns'
import { isSameOrAfter } from 'shared/lib/dateFormates'

import { ICalculationFormData, IWerCalculationJournalStore, werCalculationJournalErrorObjectInit } from '../model'

type IForceRevalidate = 'all' | 'dates' | 'overallDates'

const MIN_DATE = startOfDay(new Date('1900-01-01'))
const minDateMessage = `Дата должна быть больше или равна ${format(MIN_DATE, 'dd.MM.yyy')}`

export const validateWerCalculationForm = (
  formData: ICalculationFormData,
  forceRevalidate: IForceRevalidate = 'all',
) => {
  const errors: IWerCalculationJournalStore['formErrors'] = structuredClone(werCalculationJournalErrorObjectInit)
  if (forceRevalidate === 'all') {
    // Валидация названия
    if (!formData.title) {
      errors['title'].push('Поле должно быть заполнено')
    }
    if (formData.title.length > 100) {
      errors['title'].push('Название должно содержать не более 100 символов')
    }

    // Валидация ГЭС / каскад ГЭС
    if (formData.plantsCascades.length === 0) {
      errors['plantsCascades'].push('Поле должно быть заполнено')
    }
  }

  if (forceRevalidate === 'all' || forceRevalidate === 'dates') {
    // Валидация даты начала и конца расчета
    if (!formData.startDate || !isValid(new Date(formData.startDate))) {
      errors['startDate'].push('Поле должно быть заполнено')
    } else if (!isSameOrAfter(new Date(formData.startDate), MIN_DATE)) {
      errors['startDate'].push(minDateMessage)
    }
    if (!formData.endDate || !isValid(new Date(formData.endDate))) {
      errors['endDate'].push('Поле должно быть заполнено')
    } else if (!isSameOrAfter(new Date(formData.endDate), MIN_DATE)) {
      errors['endDate'].push(minDateMessage)
    }
    if (isAfter(new Date(formData.startDate), new Date(formData.endDate))) {
      errors['startDate'].push('Дата начала должна быть раньше даты окончания')
      errors['endDate'].push('Дата окончания должна быть позже даты начала')
    }
  }

  if (forceRevalidate === 'all' || forceRevalidate === 'overallDates') {
    // Валидация общей даты начала и конца расчета
    if (formData.overallPeriod && (!formData.overallStartDate || !isValid(new Date(formData.overallStartDate)))) {
      errors['overallStartDate'].push('Поле должно быть заполнено')
    } else if (formData.overallPeriod && !isSameOrAfter(new Date(formData.overallStartDate), MIN_DATE)) {
      errors['overallStartDate'].push(minDateMessage)
    }
    if (formData.overallPeriod && (!formData.overallEndDate || !isValid(new Date(formData.overallEndDate)))) {
      errors['overallEndDate'].push('Поле должно быть заполнено')
    } else if (formData.overallPeriod && !isSameOrAfter(new Date(formData.overallEndDate), MIN_DATE)) {
      errors['overallEndDate'].push(minDateMessage)
    }
    if (isAfter(new Date(formData.overallStartDate), new Date(formData.overallEndDate))) {
      errors['overallStartDate'].push('Общая дата начала должна быть раньше общей даты окончания')
      errors['overallEndDate'].push('Общая дата окончания должна быть позже общей даты начала')
    }
  }

  if (forceRevalidate === 'all' || forceRevalidate === 'dates' || forceRevalidate === 'overallDates') {
    if (isAfter(new Date(formData.overallStartDate), new Date(formData.startDate))) {
      errors['overallStartDate'].push('Общая дата начала должна быть раньше или равна дате начала расчета')
    }
    if (isBefore(new Date(formData.overallEndDate), new Date(formData.endDate))) {
      errors['overallEndDate'].push('Общая дата окончания должна быть позже или равна дате окончания расчета')
    }
  }

  if (forceRevalidate === 'all') {
    // Валидация описания
    if (formData?.description.length > 300) {
      errors['description'].push('Описание должно содержать не более 300 символов')
    }

    // Валидация боевых станций
    if (formData.objective && formData.objectivePlants.length === 0) {
      errors['objectivePlants'].push('Должно быть выбрано не менее одной станции')
    }
  }

  return errors
}
