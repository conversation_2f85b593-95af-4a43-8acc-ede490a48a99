import { format, subMonths } from 'date-fns'
import { makeAutoObservable, runInAction } from 'mobx'
import { validateWerCalculationForm } from 'pages/WerCalculationPage/ui/CalculationJournal/lib'
import { klona } from 'shared/lib/klona'
import { DataPickerValue } from 'shared/ui/DateRangePicker/ui/DateRangePicker.tsx'
import { RootStore } from 'stores/RootStore.ts'
import { VirtualTableCacheControlRef } from 'widgets/TableV1/lib/useTableFetchRows.ts'

import {
  decreaseSerialWerCalculation,
  deleteWerCalculation,
  getDepartments,
  getWerCalculation,
  getWerCalculationCascadesPlants,
  getWerCalculationIntervals,
  getWerCalculationObjectivePlants,
  getWerCalculations,
  IDepartmentDto,
  increaseSerialWerCalculation,
  IWerCalculationDto,
  IWerCalculationElementDto,
  IWerCalculationParams,
  saveWerCalculation,
  updateWerCalculation,
} from '../api'
import { ICalculationFormData, IWerCalculationJournalStore } from './index.ts'

export const werCalculationJournalErrorObjectInit: IWerCalculationJournalStore['formErrors'] = {
  title: [],
  department: [],
  plantsCascades: [],
  startDate: [],
  endDate: [],
  overallPeriod: [],
  overallStartDate: [],
  overallEndDate: [],
  description: [],
  objective: [],
  objectivePlants: [],
  calculationInterval: [],
}

const objectiveFilterItemsInit = [
  {
    label: 'Все',
    value: 'all',
    checked: true,
  },
  {
    label: 'Боевые',
    value: 'true',
    checked: false,
  },
  {
    label: 'Не боевые',
    value: 'false',
    checked: false,
  },
]

export class WerCalculationJournalStore {
  rootStore: RootStore
  calculations: IWerCalculationJournalStore['calculations'] = []
  cascadesPlants: IWerCalculationJournalStore['cascadesPlants'] = []
  intervals: IWerCalculationJournalStore['intervals'] = []
  objectiveFilterItems: IWerCalculationJournalStore['objectiveFilterItems'] = objectiveFilterItemsInit
  departmentsFilterItems: IWerCalculationJournalStore['departmentsFilterItems'] = []
  rowDepartments: IWerCalculationJournalStore['rowDepartments'] = []
  objectivePlants: IWerCalculationJournalStore['objectivePlants'] = []
  selectedPlant: IWerCalculationJournalStore['selectedPlant'] = null
  active: IWerCalculationJournalStore['active'] = false
  isLoading: IWerCalculationJournalStore['isLoading'] = false
  currentAbortController: IWerCalculationJournalStore['currentAbortController'] = null
  calculationParams: IWerCalculationJournalStore['calculationParams'] = {
    offset: 0,
    limit: 300,
    fromDate: format(subMonths(new Date(), 1), 'yyyy-MM-dd'),
  }
  formData: IWerCalculationJournalStore['formData'] = {
    title: '',
    department: null,
    plantsCascades: [],
    startDate: '',
    endDate: '',
    overallPeriod: false,
    overallStartDate: '',
    overallEndDate: '',
    description: '',
    objective: false,
    objectivePlants: [],
    calculationInterval: '',
  }
  formErrors: IWerCalculationJournalStore['formErrors'] = structuredClone(werCalculationJournalErrorObjectInit)
  currentRowInViewport: IWerCalculationJournalStore['currentRowInViewport'] = { topRow: 0, bottomRow: 0 }
  cacheControlRef: IWerCalculationJournalStore['cacheControlRef'] = null

  constructor(rootStore: RootStore) {
    this.rootStore = rootStore
    makeAutoObservable(this, {
      cacheControlRef: false,
      calculations: false,
      currentAbortController: false,
      currentRowInViewport: false,
    })
  }

  get availableObjectivePlants() {
    return this.objectivePlants.filter(
      (objectivePlant) => objectivePlant.objective || this.formData.objectivePlants.includes(objectivePlant.id),
    )
  }

  get someObjectivePlantsHasError() {
    return (
      this.formData.objective &&
      this.getObjectivePlantsByIds(this.formData.objectivePlants).some((objectivePlant) => objectivePlant.error)
    )
  }

  get canChangeRowPosition() {
    // Флаг, который показывает может ли пользователь менять позицию строк.
    // Позицию строк можно менять только при примененном фильтре ДЦ ВЭР, к которому относится пользователь
    // и отключенным иными фильтрами, кроме limit, offset, fromDate, toDate
    const disableRowChange = Object.entries(this.calculationParams).some(([key, value]) => {
      // Если у пользователя в фильтре указан только свой ДЦ, то разрешаем редактирование
      if (
        key === 'departmentIds' &&
        value.length === 1 &&
        value[0] === String(this.rootStore.authStore.userDetail.departmentId)
      ) {
        return false
      }
      // Игнорируем фильтры limit, offset, fromDate, toDate
      if (key === 'limit' || key === 'offset' || key === 'fromDate' || key === 'toDate') {
        return false
      }
      // Если сортировка отключена, то разрешаем редактирование
      if (key === 'sort' && value?.length === 0) {
        return false
      }

      // Если оставшиеся значение не равны undefined значит применен фильтр
      return value !== undefined
    })

    return !disableRowChange
  }

  setCurrentRowInViewport = (changes?: [number, number]) => {
    if (changes?.length === 2) {
      this.currentRowInViewport = {
        topRow: changes[0],
        bottomRow: changes[1],
      }
    }
  }

  setCacheControlRef = (cacheControlRef: VirtualTableCacheControlRef | null) => {
    this.cacheControlRef = cacheControlRef
  }

  private readonly _prepareDepartmentsNestedChildren = (
    department: IDepartmentDto,
    departmentIds: string[],
  ): IWerCalculationJournalStore['departmentsFilterItems'][0] => {
    if (department.children.length === 0) {
      return {
        label: department.name,
        value: String(department.id),
        checked: departmentIds.includes(String(department.id)),
        children: [],
      }
    }

    return {
      label: department.name,
      value: String(department.id),
      checked: departmentIds.includes(String(department.id)),
      children: department.children.map((dep) => this._prepareDepartmentsNestedChildren(dep, departmentIds)),
    }
  }

  private readonly _resetFormData = () => {
    this.formData = {
      title: '',
      department: null,
      plantsCascades: [],
      startDate: '',
      endDate: '',
      overallPeriod: false,
      overallStartDate: '',
      overallEndDate: '',
      description: '',
      objective: false,
      objectivePlants: [],
      calculationInterval: '',
    }
    this.formErrors = structuredClone(werCalculationJournalErrorObjectInit)
  }

  private readonly _populateFormData = (calculation: IWerCalculationJournalStore['calculations'][0]) => {
    this.formData = {
      title: calculation.title,
      department: calculation.department.id,
      plantsCascades: calculation.plantsCascades.map((plant) => plant.id),
      startDate: calculation.startDate,
      endDate: calculation.endDate,
      overallPeriod: calculation.overallPeriod,
      overallStartDate: calculation.overallStartDate,
      overallEndDate: calculation.overallEndDate,
      description: calculation.description,
      objective: calculation.objective,
      objectivePlants: calculation.objectivePlants.map((plant) => plant.id),
      calculationInterval: calculation.calculationInterval.code,
    }
  }

  private readonly _fetchCalculations = async (params: IWerCalculationParams) => {
    if (this.currentAbortController) {
      this.currentAbortController.abort()
    }

    const abortController = new AbortController()
    this.currentAbortController = abortController
    const signal = abortController.signal

    this.isLoading = true

    try {
      const calculations = await getWerCalculations(params, signal)

      if (this.currentAbortController !== abortController) return

      this.calculations = calculations.calculations.map((calculation) => ({ ...calculation, tabId: calculation.id }))

      return {
        rows: klona(this.calculations),
        totalRows: calculations.totalCount,
      }
    } catch (error) {
      console.log(error)
    } finally {
      if (this.currentAbortController === abortController) {
        this.currentAbortController = null
      }
      this.isLoading = false
    }
  }

  /**
   * Перезапрос данных, которые отображаются пользователю
   */
  private readonly _fetchViewedCalculations = async () => {
    let offset = this.calculationParams.offset
    let limit = this.calculationParams.limit

    if (this.currentRowInViewport.topRow < limit) {
      offset = 0
      limit = limit * 2
    } else if (this.currentRowInViewport.topRow > limit) {
      // Для корректной работы виртуализации offset должен быть кратен значению limit
      offset = Math.trunc((this.currentRowInViewport.topRow - limit) / limit) * limit
      limit = limit * 2
    }

    try {
      const calculation = await this._fetchCalculations({ ...this.calculationParams, offset, limit })
      if (this.cacheControlRef && calculation) {
        this.cacheControlRef.cache.setRows(offset, calculation?.rows)
        this.cacheControlRef.updateRows(offset, calculation?.rows.length, calculation.totalRows)
      }
    } catch (error) {
      console.log(error)
    }
  }

  initData = () => {
    this.calculationParams = {
      ...this.calculationParams,
      departmentIds: this.rootStore.authStore.userDetail.departmentId
        ? [String(this.rootStore.authStore.userDetail.departmentId)]
        : undefined,
    }
  }

  updateDateRange = (value: DataPickerValue) => {
    const [fromDate, toDate] = value
    runInAction(() => {
      this.calculationParams = {
        ...this.calculationParams,
        fromDate: fromDate ? format(fromDate, 'yyyy-MM-dd') : undefined,
        toDate: toDate ? format(toDate, 'yyyy-MM-dd') : undefined,
      }
      this._fetchViewedCalculations()
    })
  }

  fetchDepartmentsFilterItems = async () => {
    const userDepartmentId = this.rootStore.authStore.userDetail.departmentId

    try {
      const departments = await getDepartments()
      this.rowDepartments = departments
      this.departmentsFilterItems = departments.map((department) =>
        this._prepareDepartmentsNestedChildren(department, [String(userDepartmentId)]),
      )
    } catch (error) {
      console.log(error)
    }
  }

  fetchCalculations = async (offset: number, limit: number) => {
    try {
      this.calculationParams = {
        ...this.calculationParams,
        offset,
      }

      return await this._fetchCalculations({ ...this.calculationParams, offset, limit })
    } catch (error) {
      console.log(error)
    }
  }

  fetchCascadesPlants = async () => {
    try {
      const cascadesPlants = await getWerCalculationCascadesPlants()
      this.cascadesPlants = cascadesPlants.map((cascadesPlant) => ({
        ...cascadesPlant,
        label: cascadesPlant.name,
        value: cascadesPlant.id,
      }))
    } catch (error) {
      console.log(error)
    }
  }

  fetchIntervals = async () => {
    try {
      const intervals = await getWerCalculationIntervals()
      this.intervals = intervals.map((interval) => ({ ...interval, label: interval.title, value: interval.code }))
    } catch (error) {
      console.log(error)
    }
  }

  fetchObjectivePlants = async (plantsCascadesIds: number[]) => {
    try {
      const plantsCascades: IWerCalculationElementDto[] = this.cascadesPlants.filter((cascadePlant) =>
        plantsCascadesIds.includes(cascadePlant.id),
      )
      const objectivePlants = await getWerCalculationObjectivePlants(plantsCascades)
      this.objectivePlants = objectivePlants.map((objectivePlant) => ({
        ...objectivePlant,
        label: objectivePlant.name,
        value: objectivePlant.id,
        error: !objectivePlant.objective,
      }))
    } catch (error) {
      console.log(error)
    }
  }

  createCalculation = () => {
    this.active = true
    this._resetFormData()
  }

  updateCalculation = async (tabId: number) => {
    try {
      const calculation = await getWerCalculation(tabId)
      if (calculation) {
        this.selectedPlant = { ...calculation, tabId: calculation.id }
        this._populateFormData(this.selectedPlant)
      }
      this.active = true
    } catch (error) {
      console.log(error)
    }
  }

  saveCalculation = async () => {
    this.isLoading = true

    this.formErrors = validateWerCalculationForm(this.formData)
    const hasError = Object.entries(this.formErrors).some(([, value]) => value.length > 0)

    try {
      if (hasError || this.someObjectivePlantsHasError) {
        throw new Error('Валидация завершилась как минимум с одной ошибкой')
      }
      const calculationData: Partial<IWerCalculationDto> = {
        id: this.selectedPlant?.id ?? undefined,
        title: this.formData.title,
        plantsCascades: this.getCascadesPlantsByIds(this.formData.plantsCascades).map((cascadePlant) => ({
          id: cascadePlant.id,
          name: cascadePlant.name,
          type: cascadePlant.type,
        })),
        startDate: format(new Date(this.formData.startDate), 'yyyy-MM-dd'),
        endDate: format(new Date(this.formData.endDate), 'yyyy-MM-dd'),
        overallPeriod: this.formData.overallPeriod,
        overallStartDate: this.formData.overallPeriod
          ? format(new Date(this.formData.overallStartDate), 'yyyy-MM-dd')
          : undefined,
        overallEndDate: this.formData.overallPeriod
          ? format(new Date(this.formData.overallEndDate), 'yyyy-MM-dd')
          : undefined,
        description: this.formData.description,
        objective: this.formData.objective,
        objectivePlants: this.formData.objective
          ? this.getObjectivePlantsByIds(this.formData.objectivePlants).map((plant) => ({
              id: plant.id,
              name: plant.name,
              type: plant.type,
            }))
          : undefined,
        calculationInterval: this.intervals.find((interval) => interval.code === this.formData.calculationInterval)!,
      }

      if (this.selectedPlant) {
        await updateWerCalculation(this.selectedPlant.id, calculationData)
      } else {
        await saveWerCalculation(calculationData)
      }
      await this._fetchViewedCalculations()

      this.resetChanges()
    } catch (error) {
      console.log(error)
    } finally {
      this.isLoading = false
    }
  }

  deleteCalculation = async (id: IWerCalculationDto['id']) => {
    await deleteWerCalculation(id)
    await this._fetchViewedCalculations()
  }

  increaseSerial = async (id: IWerCalculationDto['id']) => {
    await increaseSerialWerCalculation(id)
    await this._fetchViewedCalculations()
  }

  decreaseSerial = async (id: IWerCalculationDto['id']) => {
    await decreaseSerialWerCalculation(id)
    await this._fetchViewedCalculations()
  }

  handleSorting: IWerCalculationJournalStore['handleSorting'] = async (sorting) => {
    this.calculationParams.sort = sorting?.map(
      (sortingItem) => `${sortingItem.columnName},${sortingItem.direction.toUpperCase()}`,
    ) as IWerCalculationParams['sort']
    await this._fetchViewedCalculations()
  }

  handleSearching: IWerCalculationJournalStore['handleSearching'] = async (searching) => {
    runInAction(() => {
      this.calculationParams = {
        ...this.calculationParams,
        serialNumber: undefined,
        title: undefined,
        changedDateTime: undefined,
        plantOrCascadeName: undefined,
        startDate: undefined,
        endDate: undefined,
        description: undefined,
        objectivePlantName: undefined,
        departmentName: undefined,
      }

      searching?.forEach((filter) => {
        const updatedValue = filter.value === '' ? undefined : filter.value
        switch (filter.columnName) {
          case 'serialNumber':
            this.calculationParams.serialNumber = updatedValue
            break
          case 'title':
            this.calculationParams.title = updatedValue
            break
          case 'changedDatetime':
            this.calculationParams.changedDateTime = updatedValue
            break
          case 'plantsCascades':
            this.calculationParams.plantOrCascadeName = updatedValue
            break
          case 'startDate':
            this.calculationParams.startDate = updatedValue
            break
          case 'endDate':
            this.calculationParams.endDate = updatedValue
            break
          case 'description':
            this.calculationParams.description = updatedValue
            break
          case 'objectivePlants':
            this.calculationParams.objectivePlantName = updatedValue
            break
          case 'department':
            this.calculationParams.departmentName = updatedValue
            break
          default:
            break
        }
      })
    })
    await this._fetchViewedCalculations()
  }

  private readonly _handleFilteringObjective = (value: string) => {
    this.objectiveFilterItems = this.objectiveFilterItems.map((item) => {
      if (item.value === value) {
        return {
          ...item,
          checked: true,
        }
      }

      return {
        ...item,
        checked: false,
      }
    })
    switch (value) {
      case 'all':
        this.calculationParams.objective = undefined
        break
      case 'true':
        this.calculationParams.objective = true
        break
      case 'false':
        this.calculationParams.objective = false
        break
    }
  }

  private readonly _handleFilteringDepartments = (values: string[]) => {
    runInAction(() => {
      this.departmentsFilterItems = this.rowDepartments.map((department) =>
        this._prepareDepartmentsNestedChildren(department, values),
      )
      this.calculationParams.departmentIds = values
    })
  }

  handleFiltering: IWerCalculationJournalStore['handleFiltering'] = (type: string, columnName: string, value: any) => {
    runInAction(() => {
      this.calculationParams = {
        ...this.calculationParams,
        offset: 0,
        limit: 300,
      }
      this.currentRowInViewport = { topRow: 0, bottomRow: 0 }
      if (type === 'radioselect') {
        if (columnName === 'objective') {
          this._handleFilteringObjective(value)
        }
      } else if (type === 'treeselect') {
        if (columnName === 'department') {
          this._handleFilteringDepartments(value)
        }
      }
      this._fetchViewedCalculations()
    })
  }

  resetChanges = () => {
    this.active = false
    this.selectedPlant = null
    this.objectivePlants = []
    this._resetFormData()
  }

  resetStore = () => {
    this.calculations = []
    this.cascadesPlants = []
    this.intervals = []
    this.objectivePlants = []
    this.selectedPlant = null
    this.active = false
    this.isLoading = false
    this.currentAbortController = null
    this.calculationParams = {
      offset: 0,
      limit: 300,
      fromDate: format(subMonths(new Date(), 1), 'yyyy-MM-dd'),
    }
    this._resetFormData()
    this.currentRowInViewport = { topRow: 0, bottomRow: 0 }
  }

  updateFormField = <K extends keyof ICalculationFormData>(field: K, value: ICalculationFormData[K]) => {
    runInAction(() => {
      this.formData = {
        ...this.formData,
        [field]: value,
      }

      // При смене дат повторно валидируем поля на форме, так как валидация в датах зависит друг от друга
      if (field === 'startDate' || field === 'endDate' || field === 'overallStartDate' || field === 'overallEndDate') {
        const datesErrors = validateWerCalculationForm(this.formData, 'all')
        this.formErrors = {
          ...this.formErrors,
          startDate: datesErrors.startDate,
          endDate: datesErrors.endDate,
          overallStartDate: datesErrors.overallStartDate,
          overallEndDate: datesErrors.overallEndDate,
        }
      } else {
        // У всех полей кроме дат после редактирования сбрасываем состояние ошибки
        this.formErrors = {
          ...this.formErrors,
          [field]: [],
        }
      }
    })
  }

  getCascadesPlantsByIds = (ids: number[]) => {
    return this.cascadesPlants.filter((cascadePlant) => ids.includes(cascadePlant.id))
  }

  getObjectivePlantsByIds = (ids: number[]) => {
    return this.objectivePlants.filter((objectivePlant) => ids.includes(objectivePlant.id))
  }
}
