// Модификатор !important допустимо использовать в тех случах, когда нельзя настроить приоритет стилей.
//
// Пояснение:
// В компоненте спредшита по бизнес-логике могут применяться несколько стилей на одну ячейку,
// у каждого стиля есть свой приоритет. При использовании модификатора !important работать с приоритетом стилей
// становиться крайне трудно.
// Пример: plantOptimized приоритетнее стиля isNotValid, по этой причине isNotValid описывается раньше чем plantOptimized.
// По правилам css в этом случае приоритетнее становиться стиль, который был описан ниже.

// Элемент с этим стилем встраивается на уровень тега body
// по этой причине его нельзя переместить в класс .spreadsheet
.htComments .htCommentTextArea {
  border-left: none;
  background-color: rgba(97, 97, 97, 0.92);
  color: #fff;
  resize: none;
}

.spreadsheet {
  color: var(--text-color);
  width: 100%;
  height: 100%;

  & .handsontable {
    font-family: var(--font-family-main), serif;

    tbody,
    thead {
      th.ht__highlight {
        background-color: var(--spreadsheet-bg-color-th-modes-plant);
      }
    }

    th {
      background-color: var(--spreadsheet-bg-color-th);
      .collapsibleIndicator {
        background: var(--spreadsheet-bg-color-th);
        box-shadow: none;
        left: 5px !important;
        right: unset !important;
      }
    }
  }

  & .htCommentCell:after {
    border-top-color: transparent;
  }

  textarea.handsontableInput {
    line-height: 16px;
    font-size: 12px;
  }

  & td,
  & th {
    // Нельзя обойтись пропом rowHeights в компоненте спредшита,
    // потому что библиотека не корректно обрабатывает значение меньше чем 23px
    // https://handsontable.com/docs/javascript-data-grid/api/options/#rowheights
    height: 16px;
    font-size: 12px;
    border-color: var(--spreadsheet-border-color);

    &.bold {
      font-weight: bold;
    }
  }

  th {
    line-height: 12px;
    align-content: center;
    & .colHeader {
      line-height: 12px;
    }
    & .rowHeader {
      line-height: 12px;
    }

    &.RSVHeaderLabel {
      color: var(--color-stage-rsv);
    }
    &.VSVGOHeaderLabel {
      color: var(--color-stage-vsvgo);
    }
    &.planningStagesDoNotMatchHeaderLabel {
      color: var(--color-planning-stages-do-not-match);
    }
    &.plantOptimized {
      background-color: var(--spreadsheet-bg-color-th-optimized-plant)
    }
    &.plantOptimizedRgu {
      background-color: var(--spreadsheet-bg-color-th-optimized-rgu)
    }
    &.plantNotOptimized {
      background-color: var(--spreadsheet-bg-color-th-not-optimized-plant)
    }
    &.plantNotOptimizedRgu {
      background-color: var(--spreadsheet-bg-color-th-not-optimized-rgu)
    }
    &.plantLimit {
      background-color: var(--spreadsheet-bg-color-th-limit-plant);
      .collapsibleIndicator {
        background: var(--spreadsheet-bg-color-th-limit-plant);
        box-shadow: none;
      }
    }
    &.plantLimitRgu {
      background-color: var(--spreadsheet-bg-color-th-limit-plant-rgu);
      .collapsibleIndicator {
        background: var(--spreadsheet-bg-color-th-limit-plant-rgu);
        box-shadow: none;
      }
    }
    &.plantModes {
      background-color: var(--spreadsheet-bg-color-th-modes-plant);
      .collapsibleIndicator {
        background: var(--spreadsheet-bg-color-th-modes-plant);
        box-shadow: none;
      }
    }
    &.plantModesRgu {
      background-color: var(--spreadsheet-bg-color-th-modes-plant-rgu);
      .collapsibleIndicator {
        background: var(--spreadsheet-bg-color-th-modes-plant-rgu);
        box-shadow: none;
      }
    }
    &.isNotValid {
      background-color: var(--spreadsheet-bg-color-error);
      & .collapsibleIndicator {
        background: inherit;
        box-shadow: none;
      }
    }
    &.acceptedBgColor {
      background-color: var(--bg-color-accept);
      .collapsibleIndicator {
        background: var(--bg-color-accept);
        box-shadow: none;
      }
    }
  }

  td {
    line-height: 16px;
    &.disabledFooter {
      color: #373737;
      background-color: var(--spreadsheet-bg-color-th);
    }
    &.disabledCell {
      color: #373737;
      background-color: var(--spreadsheet-color-disabled);
    }
    &.maxConsumptionHour {
      color: var(--red-color);
    }
    &.minConsumptionHour {
      color: var(--green-color);
    }
    &.manualAdjustments {
      color: var(--blue-light-color);
      font-weight: 700;
      font-style: italic;
    }
    &.plantOptimized {
      background-color: var(--spreadsheet-bg-color-td-optimized-plant)
    }
    &.plantOptimizedRgu {
      background-color: var(--spreadsheet-bg-color-td-optimized-rgu)
    }
    &.plantNotOptimized {
      background-color: var(--spreadsheet-bg-color-td-not-optimized-plant)
    }
    &.plantNotOptimizedRgu {
      background-color: var(--spreadsheet-bg-color-td-not-optimized-rgu)
    }
    &.plantLimit {
      background-color: var(--spreadsheet-bg-color-td-limit-plant)
    }
    &.plantLimitRgu {
      background-color: var(--spreadsheet-bg-color-td-limit-plant-rgu)
    }
    &.plantModes {
      background-color: var(--spreadsheet-bg-color-td-modes-plant)
    }
    &.isGAES {
      background-color: #fffdc7;
    }
    &.plantCountedOptimized {
      background-color: var(--spreadsheet-bg-color-th-optimized-plant)
    }
    &.plantCountedNotOptimized {
      background-color: var(--spreadsheet-bg-color-th-not-optimized-plant)
    }
    &.isNotValid {
      background-color: var(--spreadsheet-bg-color-error);
      color: var(--spreadsheet-color-error);
    }
  }

  &Sm {
    & textarea.handsontableInput {
      line-height: 15px;
    }
    td {
      line-height: 15px;
      height: 15px;
    }
    th {
      height: 15px;
    }
  }

  // Убираем отступ у чекбоксов в ячейках таблицы
  .htCheckboxRendererInput {
    margin: 0 !important;
  }
}
